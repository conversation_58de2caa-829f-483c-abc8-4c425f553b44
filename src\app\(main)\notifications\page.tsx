"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { NotificationSettings } from "@/components/notifications/notification-settings";
import { NotificationErrorBoundary } from "@/components/notifications/notification-error-boundary";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";

const NotificationsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const { handle401Error } = useAuthErrorHandler();

  // State management - simplified
  const [settings, setSettings] = useState<INotificationPreferences | null>(null);
  const [settingsLoading, setSettingsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Request management - enhanced with debouncing and deduplication
  const currentRequestRef = useRef<AbortController | null>(null);
  const lastFetchTimeRef = useRef<number>(0);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced fetch function with 400ms debouncing
  const debouncedFetchSettings = useCallback(async () => {
    if (!session?.backendTokens?.accessToken) return;

    // Clear existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set debounce timeout (400ms)
    debounceTimeoutRef.current = setTimeout(async () => {
      // Prevent multiple concurrent requests
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }

      // Rate limiting: prevent requests more frequent than every 500ms
      const now = Date.now();
      if (now - lastFetchTimeRef.current < 500) {
        return;
      }
      lastFetchTimeRef.current = now;

      // Create new abort controller for this request
      const abortController = new AbortController();
      currentRequestRef.current = abortController;

      setSettingsLoading(true);
      setError(null);

      try {
        // Use Next.js API route instead of direct backend call (following Applications pattern)
        const response = await fetch("/api/notifications/settings", {
          headers: {
            "Content-Type": "application/json",
          },
          cache: "no-store",
          signal: abortController.signal,
        });

        // Check if request was aborted
        if (abortController.signal.aborted) {
          return;
        }

        const result = await response.json();

        if (response.ok) {
          setSettings(result.data);
          setError(null);
        } else {
          if (response.status === 401) {
            handle401Error();
            return;
          }
          const errorMessage = result.message || "Failed to fetch notification settings";
          setError(errorMessage);
          setSettings(null);
        }
      } catch (error: any) {
        // Don't show error for aborted requests
        if (error.name === 'AbortError') {
          return;
        }

        console.error("Error fetching notification settings:", error);
        const errorMessage = "Failed to fetch notification settings. Please try again.";
        setError(errorMessage);
        setSettings(null);
      } finally {
        // Only update loading state if this request wasn't aborted
        if (!abortController.signal.aborted) {
          setSettingsLoading(false);
        }

        // Clear current request reference
        if (currentRequestRef.current === abortController) {
          currentRequestRef.current = null;
        }
      }
    }, 400); // 400ms debounce delay
  }, [session, handle401Error]);

  // Non-debounced fetch for immediate calls
  const fetchSettings = useCallback(async () => {
    debouncedFetchSettings();
  }, [debouncedFetchSettings]);

  // Initial data fetch - simplified
  useEffect(() => {
    if (status === "authenticated" && session) {
      fetchSettings();
    }
  }, [status, session, fetchSettings]);

  // Cleanup function to cancel ongoing requests and debounce timeouts
  useEffect(() => {
    return () => {
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Settings update handler - simplified to prevent excessive refetching
  const handleSettingsUpdate = useCallback(() => {
    // Don't automatically refetch after settings update
    // The mutation hook already handles success/error states
    // Only refetch if there's an actual need (like after a successful save)
    // Settings updated successfully - no action needed
  }, []);

  // Retry handler for error states
  const handleRetry = useCallback(() => {
    setError(null);
    fetchSettings();
  }, [fetchSettings]);

  // Loading state
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">Manage notification settings</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
        </CardHeader>
        <CardContent>
          {settingsLoading ? (
            <div className="space-y-6">
              {/* Skeleton loading for notification preferences */}
              {[1, 2, 3, 4, 5, 6].map((index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3"></div>
                      <div className="h-3 bg-gray-100 rounded animate-pulse w-2/3"></div>
                    </div>
                    <div className="h-6 w-11 bg-gray-200 rounded-full animate-pulse"></div>
                  </div>
                </div>
              ))}
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-sm text-muted-foreground">Loading notification settings...</p>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-12 space-y-6">
              <div className="text-destructive">
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                  <svg
                    className="h-8 w-8 text-red-600"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Unable to load notification settings
                </h3>
                <p className="text-sm text-gray-600 mb-4 max-w-md mx-auto">
                  {error.includes("timeout") || error.includes("network")
                    ? "The request took too long to complete. This might be due to a slow network connection or server issues."
                    : error
                  }
                </p>
                <div className="text-xs text-gray-500 mb-6">
                  <p>• Check your internet connection</p>
                  <p>• The server might be temporarily unavailable</p>
                  <p>• Try refreshing the page or contact support if the issue persists</p>
                </div>
              </div>
              <div className="flex gap-3 justify-center">
                <button
                  onClick={handleRetry}
                  disabled={settingsLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {settingsLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Retrying...
                    </>
                  ) : (
                    <>
                      <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      Try Again
                    </>
                  )}
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Refresh Page
                </button>
              </div>
            </div>
          ) : settings ? (
            <NotificationErrorBoundary>
              <NotificationSettings
                settings={settings}
                onSettingsUpdate={handleSettingsUpdate}
              />
            </NotificationErrorBoundary>
          ) : (
            <div className="text-center py-12 space-y-4">
              <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 00-15 0v5h5l-5 5-5-5h5V7a9.5 9.5 0 0119 0v10z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">No notification settings found</h3>
              <p className="text-sm text-gray-600 max-w-md mx-auto">
                We&apos;re unable to load your notification preferences at the moment. Please try refreshing the page.
              </p>
              <button
                onClick={fetchSettings}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Load Settings
              </button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationsPage;
