"use client";

import React, { useState, useEffect, use<PERSON>allback, useRef } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUpdateNotificationSettings } from "@/hooks/notifications/use-query";

interface NotificationSettingsProps {
  settings: INotificationPreferences;
  onSettingsUpdate: () => void;
}

// Notification preferences configuration with descriptions
const NOTIFICATION_PREFERENCES = [
  {
    key: "agent_assigned" as keyof INotificationPreferences,
    label: "Agent Assigned",
    description: "Notification when agent is assigned to application",
    type: "boolean" as const,
  },
  {
    key: "case_status_update" as keyof INotificationPreferences,
    label: "Case Status Update",
    description: "Updates on application status changes",
    type: "boolean" as const,
  },
  {
    key: "agent_query" as keyof INotificationPreferences,
    label: "Agent Query",
    description: "Notifications for agent queries and responses",
    type: "boolean" as const,
  },
  {
    key: "document_rejection" as keyof INotificationPreferences,
    label: "Document Rejection",
    description: "Alerts when documents are rejected",
    type: "boolean" as const,
  },
  {
    key: "missing_document_reminder_days" as keyof INotificationPreferences,
    label: "Missing Document Reminder",
    description: "Days before sending missing document reminders",
    type: "number" as const,
    min: 1,
    max: 365,
  },
  {
    key: "final_decision_issued" as keyof INotificationPreferences,
    label: "Final Decision Issued",
    description: "Final application decision notifications",
    type: "boolean" as const,
  },
];

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  settings,
  onSettingsUpdate,
}) => {
  // Initialize with settings from props - no hardcoded defaults
  const [localSettings, setLocalSettings] = useState<INotificationPreferences>(settings);
  const [hasChanges, setHasChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const updateMutation = useUpdateNotificationSettings();

  // Debounce timer ref for auto-save functionality
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize local settings from props - simplified to prevent infinite loops
  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
      setHasChanges(false);
      setValidationErrors({});
    }
  }, [settings]); // Remove localSettings from dependencies to prevent infinite loops

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Validate missing document reminder days
  const validateReminderDays = useCallback((value: number): string | null => {
    if (value < 1) return "Reminder days must be at least 1 day";
    if (value > 365) return "Reminder days cannot exceed 365 days";
    if (!Number.isInteger(value)) return "Reminder days must be a whole number";
    return null;
  }, []);

  const handleSettingChange = useCallback((
    key: keyof INotificationPreferences,
    value: boolean | number
  ) => {
    // Clear validation error for this field
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[key];
      return newErrors;
    });

    // Validate missing document reminder days if applicable
    if (key === 'missing_document_reminder_days' && typeof value === 'number') {
      const error = validateReminderDays(value);
      if (error) {
        setValidationErrors(prev => ({
          ...prev,
          [key]: error
        }));
        return; // Don't update if validation fails
      }
    }

    setLocalSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
    setHasChanges(true);
  }, [validateReminderDays]);

  const handleSave = useCallback(async () => {
    // Check for validation errors
    const hasErrors = Object.keys(validationErrors).length > 0;
    if (hasErrors) {
      return;
    }

    // Prevent multiple concurrent save operations
    if (updateMutation.isPending) {
      return;
    }

    // Clear any pending auto-save timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
      saveTimeoutRef.current = null;
    }

    try {
      await updateMutation.mutateAsync(localSettings);
      setHasChanges(false);
      setValidationErrors({});
      onSettingsUpdate();
    } catch (error) {
      console.error("Failed to update notification settings:", error);
      // Error handling is managed by the mutation hook
    }
  }, [localSettings, validationErrors, updateMutation, onSettingsUpdate]);

  const handleReset = useCallback(() => {
    if (settings) {
      setLocalSettings(settings);
      setHasChanges(false);
      setValidationErrors({});
    }
  }, [settings]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Notification Preferences</h3>
          <p className="text-sm text-muted-foreground">
            Configure how and when you receive notifications
          </p>
        </div>
        <div className="flex gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleReset}>
              Reset
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || updateMutation.isPending || Object.keys(validationErrors).length > 0}
          >
            {updateMutation.isPending ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Saving...
              </div>
            ) : (
              "Save Changes"
            )}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {NOTIFICATION_PREFERENCES.map((preference) => {
          const value = localSettings[preference.key];

          return (
            <Card key={preference.key}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">
                      {preference.label}
                    </CardTitle>
                    <CardDescription>{preference.description}</CardDescription>
                  </div>
                  {preference.type === "boolean" && (
                    <Switch
                      checked={value as boolean}
                      onCheckedChange={(checked) =>
                        handleSettingChange(preference.key, checked)
                      }
                      disabled={updateMutation.isPending}
                    />
                  )}
                </div>
              </CardHeader>

              {preference.type === "number" && (
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium">
                        {preference.label} (days)
                      </Label>
                      <Input
                        type="number"
                        min={preference.min}
                        max={preference.max}
                        value={value as number}
                        onChange={(e) => {
                          const numValue = parseInt(e.target.value) || preference.min || 1;
                          handleSettingChange(preference.key, numValue);
                        }}
                        className={`w-32 mt-2 ${
                          validationErrors[preference.key]
                            ? "border-destructive"
                            : ""
                        }`}
                        placeholder={preference.min?.toString() || "1"}
                        disabled={updateMutation.isPending}
                      />
                      {validationErrors[preference.key] && (
                        <p className="text-xs text-destructive mt-1">
                          {validationErrors[preference.key]}
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        Number of days before sending reminder notifications
                      </p>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>
    </div>
  );
};
