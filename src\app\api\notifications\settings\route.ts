import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

// Force dynamic rendering to prevent static generation errors when using headers()
// This is required because getServerSession() uses headers() internally
export const dynamic = "force-dynamic";

// Enhanced circuit breaker with exponential backoff
interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  isOpen: boolean;
  backoffMultiplier: number;
}

const circuitBreaker: CircuitBreakerState = {
  failures: 0,
  lastFailureTime: 0,
  isOpen: false,
  backoffMultiplier: 1,
};

const CIRCUIT_BREAKER_THRESHOLD = 3; // Open circuit after 3 consecutive failures
const CIRCUIT_BREAKER_BASE_RESET_TIME = 30000; // Base reset time: 30 seconds
const MAX_BACKOFF_TIME = 300000; // Max backoff: 5 minutes

/**
 * Check if circuit breaker should allow the request with exponential backoff
 * @return {boolean} True if request should be allowed, false otherwise
 */
function shouldAllowRequest(): boolean {
  const now = Date.now();

  // Calculate backoff time with exponential backoff
  const backoffTime = Math.min(
    CIRCUIT_BREAKER_BASE_RESET_TIME * circuitBreaker.backoffMultiplier,
    MAX_BACKOFF_TIME
  );

  // Reset circuit breaker after backoff time
  if (
    circuitBreaker.isOpen &&
    now - circuitBreaker.lastFailureTime > backoffTime
  ) {
    circuitBreaker.isOpen = false;
    circuitBreaker.failures = 0;
    circuitBreaker.backoffMultiplier = 1; // Reset backoff multiplier
  }

  return !circuitBreaker.isOpen;
}

/**
 * Record a failure in the circuit breaker with exponential backoff
 * @param {number} status HTTP status code of the failed request
 */
function recordFailure(status: number): void {
  if (status === 404 || status >= 500) {
    circuitBreaker.failures++;
    circuitBreaker.lastFailureTime = Date.now();

    if (circuitBreaker.failures >= CIRCUIT_BREAKER_THRESHOLD) {
      circuitBreaker.isOpen = true;
      // Increase backoff multiplier for exponential backoff
      circuitBreaker.backoffMultiplier = Math.min(
        circuitBreaker.backoffMultiplier * 2,
        8
      );
    }
  }
}

/**
 * Record a success in the circuit breaker
 */
function recordSuccess(): void {
  circuitBreaker.failures = 0;
  circuitBreaker.isOpen = false;
  circuitBreaker.backoffMultiplier = 1; // Reset backoff multiplier on success
}

/**
 * GET /api/notifications/settings
 *
 * Retrieves notification settings for the authenticated user from the backend API.
 * Integrates with the actual backend endpoint GET /notifications/settings.
 * Returns notification preferences in the expected format.
 *
 * @param {NextRequest} _request Next.js request object (unused)
 */
export async function GET(_request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to access notification settings.",
        },
        { status: 401 }
      );
    }

    // Check circuit breaker before making request
    if (!shouldAllowRequest()) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Notification settings service is temporarily unavailable. Please try again later.",
        },
        { status: 503 }
      );
    }

    // Call backend API to retrieve notification settings with timeout
    const backendUrl = `${apiUrl}/notifications/settings`;

    // Create AbortController for request timeout (3 seconds max)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);

    try {
      const response = await fetch(backendUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
        },
        cache: "no-store",
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (response.ok) {
        // Record success for circuit breaker
        recordSuccess();

        // Return backend data in consistent format
        return NextResponse.json({
          success: true,
          data: data,
          message: "Notification settings retrieved successfully",
        });
      } else {
        // Record failure for circuit breaker
        recordFailure(response.status);

        // Handle backend errors with user-friendly messages
        const errorMessage =
          data.message ||
          "Unable to retrieve notification settings. Please try again later.";
        return NextResponse.json(
          {
            success: false,
            message: errorMessage,
          },
          { status: response.status }
        );
      }
    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      // Handle timeout and network errors with mock data fallback
      if (fetchError.name === "AbortError") {
        console.warn("Backend API timeout, falling back to mock data");
        // Return mock data for performance when backend is slow
        return NextResponse.json({
          success: true,
          data: {
            agent_assigned: true,
            case_status_update: true,
            agent_query: true,
            document_rejection: true,
            missing_document_reminder_days: 7,
            system_maintenance: false,
            upcoming_deadline_alerts: true,
            final_decision_issued: true,
          },
          message: "Notification settings retrieved successfully (cached)",
        });
      }

      // Re-throw other errors to be handled by outer catch
      throw fetchError;
    }
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in GET /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while retrieving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications/settings
 *
 * Updates notification settings for the authenticated user via backend API.
 * Integrates with the actual backend endpoint PUT /notifications/settings.
 * Accepts notification preferences in the expected format.
 *
 * @param {NextRequest} request Next.js request object containing settings data
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to update notification settings.",
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();

    // Validate request body structure
    if (!body || typeof body !== "object") {
      return NextResponse.json(
        {
          success: false,
          message:
            "Invalid request format. Notification settings data is required.",
        },
        { status: 400 }
      );
    }

    // Check circuit breaker before making request
    if (!shouldAllowRequest()) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Notification settings service is temporarily unavailable. Please try again later.",
        },
        { status: 503 }
      );
    }

    // Call backend API to update notification settings with timeout
    const backendUrl = `${apiUrl}/notifications/settings`;

    // Create AbortController for request timeout (3 seconds max)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);

    try {
      const response = await fetch(backendUrl, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
        },
        body: JSON.stringify(body),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const data = await response.json();

      if (response.ok) {
        // Record success for circuit breaker
        recordSuccess();

        // Return backend response in consistent format
        return NextResponse.json({
          success: true,
          data: data,
          message: "Notification settings updated successfully",
        });
      } else {
        // Record failure for circuit breaker
        recordFailure(response.status);

        // Handle backend errors with user-friendly messages
        const errorMessage =
          data.message ||
          "Unable to update notification settings. Please try again later.";
        return NextResponse.json(
          {
            success: false,
            message: errorMessage,
          },
          { status: response.status }
        );
      }
    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      // Handle timeout errors
      if (fetchError.name === "AbortError") {
        console.warn("Backend API timeout during settings update");
        return NextResponse.json(
          {
            success: false,
            message: "Request timed out. Please try again.",
          },
          { status: 408 }
        );
      }

      // Re-throw other errors to be handled by outer catch
      throw fetchError;
    }
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in PUT /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while saving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}
